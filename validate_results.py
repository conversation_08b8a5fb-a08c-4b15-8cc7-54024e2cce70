#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

# 读取原始数据
df_orig = pd.read_excel('202411.xlsx')
# 读取处理结果
df_result = pd.read_excel('202411_processed.xlsx')

print('=== 验证计算结果 ===')
print('验证第1条记录的风速第1层数据:')

# 提取第1条记录第1层风速的原始数据
row_idx = 0
layer = 1
values = []
flags = []

for i in range(1, 61):
    value_col = f'风速第{layer}层第{i}个采样值'
    flag_col = f'风速第{layer}层第{i}个标志'
    if value_col in df_orig.columns and flag_col in df_orig.columns:
        values.append(df_orig.loc[row_idx, value_col])
        flags.append(df_orig.loc[row_idx, flag_col])

values = np.array(values, dtype=float)
flags = np.array(flags, dtype=int)

# 过滤有效数据
valid_mask = (flags == 0) | (flags == 1)
valid_data = values[valid_mask]

print(f'原始采样值数量: {len(values)}')
print(f'有效数据数量: {len(valid_data)}')
print(f'有效数据范围: {valid_data.min():.3f} - {valid_data.max():.3f}')
print(f'计算的平均值: {np.mean(valid_data):.3f}')
print(f'计算的标准差: {np.std(valid_data, ddof=1):.3f}')

print(f'\n程序输出的结果:')
print(f'Speed 140 m A: {df_result.loc[0, "Speed 140 m A"]:.3f}')
print(f'Speed 140 m A SD: {df_result.loc[0, "Speed 140 m A SD"]:.3f}')
print(f'Speed 140 m A Max: {df_result.loc[0, "Speed 140 m A Max"]:.3f}')
print(f'Speed 140 m A Min: {df_result.loc[0, "Speed 140 m A Min"]:.3f}')

print('\n验证风向计算（第1条记录第2层）:')
# 提取风向数据
layer = 2
values = []
flags = []

for i in range(1, 61):
    value_col = f'风向第{layer}层第{i}个采样值'
    flag_col = f'风向第{layer}层第{i}个标志'
    if value_col in df_orig.columns and flag_col in df_orig.columns:
        values.append(df_orig.loc[row_idx, value_col])
        flags.append(df_orig.loc[row_idx, flag_col])

values = np.array(values, dtype=float)
flags = np.array(flags, dtype=int)
valid_mask = (flags == 0) | (flags == 1)
valid_data = values[valid_mask]

print(f'风向有效数据数量: {len(valid_data)}')
print(f'风向数据范围: {valid_data.min():.1f}° - {valid_data.max():.1f}°')

# 计算风向平均值
rad = np.radians(valid_data)
mean_x = np.mean(np.cos(rad))
mean_y = np.mean(np.sin(rad))
mean_dir = np.degrees(np.arctan2(mean_y, mean_x))
if mean_dir < 0:
    mean_dir += 360

print(f'计算的平均风向: {mean_dir:.2f}°')
print(f'程序输出的风向: {df_result.loc[0, "Direction 125 m"]:.2f}°')

print('\n验证温度数据（第1条记录第1层）:')
# 提取温度数据
layer = 1
values = []
flags = []

for i in range(1, 7):  # 温度只有6个采样值
    value_col = f'温度第{layer}层第{i}个采样值'
    flag_col = f'温度第{layer}层第{i}个标志'
    if value_col in df_orig.columns and flag_col in df_orig.columns:
        values.append(df_orig.loc[row_idx, value_col])
        flags.append(df_orig.loc[row_idx, flag_col])

values = np.array(values, dtype=float)
flags = np.array(flags, dtype=int)
valid_mask = (flags == 0) | (flags == 1)
valid_data = values[valid_mask]

print(f'温度有效数据数量: {len(valid_data)}')
print(f'温度数据范围: {valid_data.min():.1f}°C - {valid_data.max():.1f}°C')
print(f'计算的平均温度: {np.mean(valid_data):.2f}°C')
print(f'程序输出的温度: {df_result.loc[0, "Temperature 10 m"]:.2f}°C')

print('\n=== 输出文件格式验证 ===')
print(f'输出文件形状: {df_result.shape}')
print(f'期望的列数: 61')
print(f'实际列数: {len(df_result.columns)}')
print('格式匹配:', '✓' if len(df_result.columns) == 61 else '✗')

print('\n=== 数据完整性检查 ===')
print('各列的非空数据统计:')
non_null_counts = df_result.count()
print(f'Date/Time: {non_null_counts["Date/Time"]}/30')
print(f'Speed 140 m A: {non_null_counts["Speed 140 m A"]}/30')
print(f'Direction 125 m: {non_null_counts["Direction 125 m"]}/30')
print(f'Temperature 10 m: {non_null_counts["Temperature 10 m"]}/30')
print(f'Humidity 10 m: {non_null_counts["Humidity 10 m"]}/30')
