#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新疆风能站CF卡文件10分钟统计值处理程序
作者: AI Assistant
日期: 2024-07-31
功能: 处理风能站原始数据，计算10分钟统计值
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wind_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WindStationProcessor:
    """风能站数据处理器"""
    
    def __init__(self):
        """初始化处理器"""
        # 基于分析的层高度映射关系
        self.wind_speed_layer_mapping = {
            1: '140m_A',  # 第1层对应140米A传感器
            2: '125m',    # 第2层对应125米
            3: '115m',    # 第3层对应115米
            4: '90m',     # 第4层对应90米
            5: '60m'      # 第5层对应60米
        }
        
        self.wind_direction_layer_mapping = {
            2: '125m',    # 第2层对应125米
            4: '90m',     # 第4层对应90米
        }
        
        # 输出列定义
        self.output_columns = [
            'Date/Time', 'voltage',
            # 风速列 - 140m A传感器
            'Speed 140 m A', 'Speed 140 m A SD', 'Speed 140 m A Max', 'Speed 140 m A Min', 'Speed 140 m A XMax',
            # 风速列 - 140m B传感器  
            'Speed 140 m B', 'Speed 140 m B SD', 'Speed 140 m B Max', 'Speed 140 m B Min', 'Speed 140 m B XMax',
            # 风速列 - 其他高度
            'Speed 125 m', 'Speed 125 m SD', 'Speed 125 m Max', 'Speed 125 m Min', 'Speed 125 m  XMax',
            'Speed 115 m', 'Speed 115 m SD', 'Speed 115 m Max', 'Speed 115 m Min', 'Speed 115 m  XMax',
            'Speed 90 m', 'Speed 90 m SD', 'Speed 90 m Max', 'Speed 90 m Min', 'Speed 90 m  XMax',
            'Speed 60 m', 'Speed 60 m SD', 'Speed 60 m Max', 'Speed 60 m Min', 'Speed 60 m  XMax',
            'Speed 10 m', 'Speed 10 m SD', 'Speed 10 m Max', 'Speed 10 m Min', 'Speed 10 m  XMax',
            # 风向列
            'Direction 125 m', 'Direction 125 m SD', 'Direction 125 m Max', 'Direction 125 m Min',
            'Direction 90 m', 'Direction 90 m SD', 'Direction 90 m Max', 'Direction 90 m Min',
            'Direction 10 m', 'Direction 10 m SD', 'Direction 10 m Max', 'Direction 10 m Min',
            # 温度列
            'Temperature 10 m', 'Temperature 10 m SD', 'Temperature 10 m Max', 'Temperature 10 m Min',
            # 气压列
            'Pressure 10 m', 'Pressure 10 m SD', 'Pressure 10 m Max', 'Pressure 10 m Min',
            # 湿度列
            'Humidity 10 m', 'Humidity 10 m SD', 'Humidity 10 m Max', 'Humidity 10 m Min'
        ]
        
    def filter_valid_data(self, values, flags):
        """
        根据标志位过滤有效数据
        
        Args:
            values: 采样值数组
            flags: 标志位数组 (0=正常, 1=可疑, 2=错误)
            
        Returns:
            过滤后的有效数据
        """
        try:
            # 转换为numpy数组
            values = np.array(values, dtype=float)
            flags = np.array(flags, dtype=int)
            
            # 过滤有效数据（接受正常和可疑数据，排除错误数据）
            valid_mask = (flags == 0) | (flags == 1)
            valid_data = values[valid_mask]
            
            # 如果没有有效数据，返回空数组
            if len(valid_data) == 0:
                logger.warning("没有有效数据")
                return np.array([])
                
            return valid_data
            
        except Exception as e:
            logger.error(f"数据过滤错误: {e}")
            return np.array([])
    
    def calculate_wind_direction_stats(self, directions):
        """
        计算风向统计值，考虑角度的循环特性
        
        Args:
            directions: 风向角度数组
            
        Returns:
            包含统计值的字典
        """
        try:
            if len(directions) == 0:
                return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
            
            # 转换为弧度
            rad = np.radians(directions)
            
            # 计算平均风向（使用向量平均法）
            mean_x = np.mean(np.cos(rad))
            mean_y = np.mean(np.sin(rad))
            mean_dir = np.degrees(np.arctan2(mean_y, mean_x))
            
            # 确保角度在0-360范围内
            if mean_dir < 0:
                mean_dir += 360
            
            # 计算标准差（角度标准差）
            # 使用向量长度来计算标准差
            vector_length = np.sqrt(mean_x**2 + mean_y**2)
            if vector_length > 0:
                std_dev = np.degrees(np.sqrt(-2 * np.log(vector_length)))
            else:
                std_dev = 180  # 如果向量长度为0，标准差为最大值
            
            return {
                'mean': round(mean_dir, 2),
                'std': round(std_dev, 2),
                'max': round(np.max(directions), 2),
                'min': round(np.min(directions), 2)
            }
            
        except Exception as e:
            logger.error(f"风向统计计算错误: {e}")
            return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
    
    def calculate_basic_stats(self, values):
        """
        计算基本统计值
        
        Args:
            values: 数值数组
            
        Returns:
            包含统计值的字典
        """
        try:
            if len(values) == 0:
                return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
            
            return {
                'mean': round(np.mean(values), 3),
                'std': round(np.std(values, ddof=1), 3),  # 样本标准差
                'max': round(np.max(values), 3),
                'min': round(np.min(values), 3)
            }
            
        except Exception as e:
            logger.error(f"基本统计计算错误: {e}")
            return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
    
    def extract_layer_data(self, df, row_idx, element_type, layer_num):
        """
        提取指定层的采样数据
        
        Args:
            df: 数据框
            row_idx: 行索引
            element_type: 要素类型 ('风向', '风速', '温度', '湿度')
            layer_num: 层数
            
        Returns:
            (values, flags): 采样值和标志位数组
        """
        try:
            values = []
            flags = []
            
            # 构建列名模式
            for i in range(1, 61):  # 60个采样值
                value_col = f'{element_type}第{layer_num}层第{i}个采样值'
                flag_col = f'{element_type}第{layer_num}层第{i}个标志'
                
                if value_col in df.columns and flag_col in df.columns:
                    values.append(df.loc[row_idx, value_col])
                    flags.append(df.loc[row_idx, flag_col])
            
            return np.array(values), np.array(flags)
            
        except Exception as e:
            logger.error(f"提取层数据错误: {e}")
            return np.array([]), np.array([])
    
    def process_single_record(self, df, row_idx):
        """
        处理单条记录，计算10分钟统计值
        
        Args:
            df: 输入数据框
            row_idx: 行索引
            
        Returns:
            包含统计值的字典
        """
        try:
            result = {}
            
            # 时间处理
            original_time = df.loc[row_idx, '原始时间']
            formatted_time = df.loc[row_idx, '第263字节时间']
            result['Date/Time'] = formatted_time
            result['voltage'] = np.nan  # 电压值暂时设为NaN，需要根据实际情况调整
            
            # 处理风速数据
            for layer, height in self.wind_speed_layer_mapping.items():
                values, flags = self.extract_layer_data(df, row_idx, '风速', layer)
                valid_data = self.filter_valid_data(values, flags)
                stats = self.calculate_basic_stats(valid_data)
                
                if height == '140m_A':
                    result['Speed 140 m A'] = stats['mean']
                    result['Speed 140 m A SD'] = stats['std']
                    result['Speed 140 m A Max'] = stats['max']
                    result['Speed 140 m A Min'] = stats['min']
                    result['Speed 140 m A XMax'] = stats['max']  # XMax暂时等于Max
                    
                    # 140m B传感器暂时使用相同数据（需要根据实际情况调整）
                    result['Speed 140 m B'] = stats['mean']
                    result['Speed 140 m B SD'] = stats['std']
                    result['Speed 140 m B Max'] = stats['max']
                    result['Speed 140 m B Min'] = stats['min']
                    result['Speed 140 m B XMax'] = stats['max']
                elif height == '125m':
                    result['Speed 125 m'] = stats['mean']
                    result['Speed 125 m SD'] = stats['std']
                    result['Speed 125 m Max'] = stats['max']
                    result['Speed 125 m Min'] = stats['min']
                    result['Speed 125 m  XMax'] = stats['max']
                elif height == '115m':
                    result['Speed 115 m'] = stats['mean']
                    result['Speed 115 m SD'] = stats['std']
                    result['Speed 115 m Max'] = stats['max']
                    result['Speed 115 m Min'] = stats['min']
                    result['Speed 115 m  XMax'] = stats['max']
                elif height == '90m':
                    result['Speed 90 m'] = stats['mean']
                    result['Speed 90 m SD'] = stats['std']
                    result['Speed 90 m Max'] = stats['max']
                    result['Speed 90 m Min'] = stats['min']
                    result['Speed 90 m  XMax'] = stats['max']
                elif height == '60m':
                    result['Speed 60 m'] = stats['mean']
                    result['Speed 60 m SD'] = stats['std']
                    result['Speed 60 m Max'] = stats['max']
                    result['Speed 60 m Min'] = stats['min']
                    result['Speed 60 m  XMax'] = stats['max']
            
            # 10m风速暂时设为NaN（需要根据实际情况调整）
            result['Speed 10 m'] = np.nan
            result['Speed 10 m SD'] = np.nan
            result['Speed 10 m Max'] = np.nan
            result['Speed 10 m Min'] = np.nan
            result['Speed 10 m  XMax'] = np.nan

            # 处理风向数据
            for layer, height in self.wind_direction_layer_mapping.items():
                values, flags = self.extract_layer_data(df, row_idx, '风向', layer)
                valid_data = self.filter_valid_data(values, flags)
                stats = self.calculate_wind_direction_stats(valid_data)

                if height == '125m':
                    result['Direction 125 m'] = stats['mean']
                    result['Direction 125 m SD'] = stats['std']
                    result['Direction 125 m Max'] = stats['max']
                    result['Direction 125 m Min'] = stats['min']
                elif height == '90m':
                    result['Direction 90 m'] = stats['mean']
                    result['Direction 90 m SD'] = stats['std']
                    result['Direction 90 m Max'] = stats['max']
                    result['Direction 90 m Min'] = stats['min']

            # 10m风向暂时设为NaN（需要根据实际情况调整）
            result['Direction 10 m'] = np.nan
            result['Direction 10 m SD'] = np.nan
            result['Direction 10 m Max'] = np.nan
            result['Direction 10 m Min'] = np.nan

            # 处理温度数据（假设第1层对应10m）
            values, flags = self.extract_layer_data(df, row_idx, '温度', 1)
            valid_data = self.filter_valid_data(values, flags)
            stats = self.calculate_basic_stats(valid_data)

            result['Temperature 10 m'] = stats['mean']
            result['Temperature 10 m SD'] = stats['std']
            result['Temperature 10 m Max'] = stats['max']
            result['Temperature 10 m Min'] = stats['min']

            # 处理湿度数据（假设第1层对应10m）
            values, flags = self.extract_layer_data(df, row_idx, '湿度', 1)
            valid_data = self.filter_valid_data(values, flags)
            stats = self.calculate_basic_stats(valid_data)

            result['Humidity 10 m'] = stats['mean']
            result['Humidity 10 m SD'] = stats['std']
            result['Humidity 10 m Max'] = stats['max']
            result['Humidity 10 m Min'] = stats['min']

            # 气压数据暂时设为NaN（当前数据中无气压数据）
            result['Pressure 10 m'] = np.nan
            result['Pressure 10 m SD'] = np.nan
            result['Pressure 10 m Max'] = np.nan
            result['Pressure 10 m Min'] = np.nan

            return result

        except Exception as e:
            logger.error(f"处理单条记录错误: {e}")
            return {}

    def process_file(self, input_file, output_file):
        """
        处理整个文件

        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
        """
        try:
            logger.info(f"开始处理文件: {input_file}")

            # 读取输入文件
            df = pd.read_excel(input_file)
            logger.info(f"读取到 {len(df)} 条记录")

            # 处理每条记录
            results = []
            for idx in range(len(df)):
                logger.info(f"处理第 {idx+1}/{len(df)} 条记录")
                result = self.process_single_record(df, idx)
                if result:
                    results.append(result)

            # 创建输出数据框
            output_df = pd.DataFrame(results, columns=self.output_columns)

            # 保存到文件
            output_df.to_excel(output_file, index=False)
            logger.info(f"处理完成，结果保存到: {output_file}")

            return output_df

        except Exception as e:
            logger.error(f"处理文件错误: {e}")
            return None

def main():
    """主函数"""
    try:
        # 创建处理器
        processor = WindStationProcessor()

        # 处理文件
        input_file = '202411.xlsx'
        output_file = '202411_processed.xlsx'

        result = processor.process_file(input_file, output_file)

        if result is not None:
            print(f"处理成功！输出文件: {output_file}")
            print(f"处理了 {len(result)} 条记录")
            print("\n前5行结果预览:")
            print(result.head())
        else:
            print("处理失败！")

    except Exception as e:
        logger.error(f"主函数错误: {e}")
        print(f"程序运行错误: {e}")

if __name__ == "__main__":
    main()
