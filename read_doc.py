#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 分析输入输出映射关系
print('=== 分析输入输出映射关系 ===')

# 读取输入数据
df = pd.read_excel('202411.xlsx')

# 读取输出格式
output_df = pd.read_excel('新疆风能站-CF卡文件10分钟统计值算法详解.xlsx')
output_cols = output_df.columns.tolist()

print('输出格式要求的高度层:')

# 提取风速相关列
speed_cols = [col for col in output_cols if 'Speed' in col and 'm' in col]
print('风速高度层:')
unique_speed_heights = set()
for col in speed_cols:
    if 'SD' not in col and 'Max' not in col and 'Min' not in col and 'XMax' not in col:
        print(f'  {col}')
        # 提取高度信息
        if '140 m A' in col:
            unique_speed_heights.add('140m_A')
        elif '140 m B' in col:
            unique_speed_heights.add('140m_B')
        elif '125 m' in col:
            unique_speed_heights.add('125m')
        elif '115 m' in col:
            unique_speed_heights.add('115m')
        elif '90 m' in col:
            unique_speed_heights.add('90m')
        elif '60 m' in col:
            unique_speed_heights.add('60m')
        elif '10 m' in col:
            unique_speed_heights.add('10m')

# 提取风向相关列
direction_cols = [col for col in output_cols if 'Direction' in col and 'm' in col]
print('\n风向高度层:')
unique_direction_heights = set()
for col in direction_cols:
    if 'SD' not in col and 'Max' not in col and 'Min' not in col:
        print(f'  {col}')
        if '125 m' in col:
            unique_direction_heights.add('125m')
        elif '90 m' in col:
            unique_direction_heights.add('90m')
        elif '10 m' in col:
            unique_direction_heights.add('10m')

print(f'\n输入数据观测层数:')
print(f'风向观测层数: {df["风向观测层数"].iloc[0]}')
print(f'风速观测层数: {df["风速观测层数"].iloc[0]}')
print(f'温度观测层数: {df["温度观测层数"].iloc[0]}')
print(f'湿度观测层数: {df["湿度观测层数"].iloc[0]}')

print(f'\n推断的层高度映射关系:')
print('风速层映射:')
print('  第1层 -> 140m (A传感器)')
print('  第2层 -> 125m')
print('  第3层 -> 115m')
print('  第4层 -> 90m')
print('  第5层 -> 60m')

print('\n风向层映射:')
print('  第2层 -> 125m')
print('  第4层 -> 90m')

print('\n温度/湿度层映射:')
print('  第1层 -> 10m')
