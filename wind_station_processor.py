#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新疆风能站CF卡文件10分钟统计值处理程序
作者: AI Assistant
日期: 2024-07-31
功能: 处理风能站原始数据，计算10分钟统计值
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wind_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WindStationProcessor:
    """风能站数据处理器"""
    
    def __init__(self):
        """初始化处理器"""
        # 高度层映射（根据实际情况可能需要调整）
        self.layer_height_mapping = {
            1: '140m',  # 第1层对应140米
            2: '125m',  # 第2层对应125米  
            3: '115m',  # 第3层对应115米
            4: '90m',   # 第4层对应90米
            5: '60m'    # 第5层对应60米
        }
        
        # 输出列定义
        self.output_columns = [
            'Date/Time', 'voltage',
            # 风速列 - 140m A传感器
            'Speed 140 m A', 'Speed 140 m A SD', 'Speed 140 m A Max', 'Speed 140 m A Min', 'Speed 140 m A XMax',
            # 风速列 - 140m B传感器  
            'Speed 140 m B', 'Speed 140 m B SD', 'Speed 140 m B Max', 'Speed 140 m B Min', 'Speed 140 m B XMax',
            # 风速列 - 其他高度
            'Speed 125 m', 'Speed 125 m SD', 'Speed 125 m Max', 'Speed 125 m Min', 'Speed 125 m  XMax',
            'Speed 115 m', 'Speed 115 m SD', 'Speed 115 m Max', 'Speed 115 m Min', 'Speed 115 m  XMax',
            'Speed 90 m', 'Speed 90 m SD', 'Speed 90 m Max', 'Speed 90 m Min', 'Speed 90 m  XMax',
            'Speed 60 m', 'Speed 60 m SD', 'Speed 60 m Max', 'Speed 60 m Min', 'Speed 60 m  XMax',
            'Speed 10 m', 'Speed 10 m SD', 'Speed 10 m Max', 'Speed 10 m Min', 'Speed 10 m  XMax',
            # 风向列
            'Direction 125 m', 'Direction 125 m SD', 'Direction 125 m Max', 'Direction 125 m Min',
            'Direction 90 m', 'Direction 90 m SD', 'Direction 90 m Max', 'Direction 90 m Min',
            'Direction 10 m', 'Direction 10 m SD', 'Direction 10 m Max', 'Direction 10 m Min',
            # 温度列
            'Temperature 10 m', 'Temperature 10 m SD', 'Temperature 10 m Max', 'Temperature 10 m Min',
            # 气压列
            'Pressure 10 m', 'Pressure 10 m SD', 'Pressure 10 m Max', 'Pressure 10 m Min',
            # 湿度列
            'Humidity 10 m', 'Humidity 10 m SD', 'Humidity 10 m Max', 'Humidity 10 m Min'
        ]
        
    def filter_valid_data(self, values, flags):
        """
        根据标志位过滤有效数据
        
        Args:
            values: 采样值数组
            flags: 标志位数组 (0=正常, 1=可疑, 2=错误)
            
        Returns:
            过滤后的有效数据
        """
        try:
            # 转换为numpy数组
            values = np.array(values)
            flags = np.array(flags)
            
            # 过滤有效数据（接受正常和可疑数据，排除错误数据）
            valid_mask = (flags == 0) | (flags == 1)
            valid_data = values[valid_mask]
            
            # 如果没有有效数据，返回空数组
            if len(valid_data) == 0:
                logger.warning("没有有效数据")
                return np.array([])
                
            return valid_data
            
        except Exception as e:
            logger.error(f"数据过滤错误: {e}")
            return np.array([])
    
    def calculate_wind_direction_stats(self, directions):
        """
        计算风向统计值，考虑角度的循环特性
        
        Args:
            directions: 风向角度数组
            
        Returns:
            包含统计值的字典
        """
        try:
            if len(directions) == 0:
                return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
            
            # 转换为弧度
            rad = np.radians(directions)
            
            # 计算平均风向（使用向量平均法）
            mean_x = np.mean(np.cos(rad))
            mean_y = np.mean(np.sin(rad))
            mean_dir = np.degrees(np.arctan2(mean_y, mean_x))
            
            # 确保角度在0-360范围内
            if mean_dir < 0:
                mean_dir += 360
            
            # 计算标准差（角度标准差）
            # 使用向量长度来计算标准差
            vector_length = np.sqrt(mean_x**2 + mean_y**2)
            if vector_length > 0:
                std_dev = np.degrees(np.sqrt(-2 * np.log(vector_length)))
            else:
                std_dev = 180  # 如果向量长度为0，标准差为最大值
            
            return {
                'mean': round(mean_dir, 2),
                'std': round(std_dev, 2),
                'max': round(np.max(directions), 2),
                'min': round(np.min(directions), 2)
            }
            
        except Exception as e:
            logger.error(f"风向统计计算错误: {e}")
            return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
    
    def calculate_basic_stats(self, values):
        """
        计算基本统计值
        
        Args:
            values: 数值数组
            
        Returns:
            包含统计值的字典
        """
        try:
            if len(values) == 0:
                return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
            
            return {
                'mean': round(np.mean(values), 3),
                'std': round(np.std(values, ddof=1), 3),  # 样本标准差
                'max': round(np.max(values), 3),
                'min': round(np.min(values), 3)
            }
            
        except Exception as e:
            logger.error(f"基本统计计算错误: {e}")
            return {'mean': np.nan, 'std': np.nan, 'max': np.nan, 'min': np.nan}
    
    def extract_layer_data(self, df, row_idx, element_type, layer_num):
        """
        提取指定层的采样数据
        
        Args:
            df: 数据框
            row_idx: 行索引
            element_type: 要素类型 ('风向', '风速', '温度', '湿度')
            layer_num: 层数
            
        Returns:
            (values, flags): 采样值和标志位数组
        """
        try:
            values = []
            flags = []
            
            # 构建列名模式
            for i in range(1, 61):  # 60个采样值
                value_col = f'{element_type}第{layer_num}层第{i}个采样值'
                flag_col = f'{element_type}第{layer_num}层第{i}个标志'
                
                if value_col in df.columns and flag_col in df.columns:
                    values.append(df.loc[row_idx, value_col])
                    flags.append(df.loc[row_idx, flag_col])
            
            return np.array(values), np.array(flags)
            
        except Exception as e:
            logger.error(f"提取层数据错误: {e}")
            return np.array([]), np.array([])
